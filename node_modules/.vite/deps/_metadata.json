{"hash": "ea90cde7", "configHash": "7827db2b", "lockfileHash": "f29da481", "browserHash": "f8462f88", "optimized": {"@heroicons/vue/24/outline": {"src": "../../@heroicons/vue/24/outline/esm/index.js", "file": "@heroicons_vue_24_outline.js", "fileHash": "b5588f53", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "a0761b8a", "needsInterop": false}, "crypto-js": {"src": "../../crypto-js/index.js", "file": "crypto-js.js", "fileHash": "5d72c620", "needsInterop": true}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "a9a0f142", "needsInterop": false}, "pinia-plugin-persistedstate": {"src": "../../pinia-plugin-persistedstate/dist/index.js", "file": "pinia-plugin-persistedstate.js", "fileHash": "7d74bb7b", "needsInterop": false}, "vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "ad29112b", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "d28d7048", "needsInterop": false}}, "chunks": {"chunk-ZY5X6FX7": {"file": "chunk-ZY5X6FX7.js"}, "chunk-5FUTL2UF": {"file": "chunk-5FUTL2UF.js"}}}